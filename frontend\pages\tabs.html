<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签页模式 - Makrite管理系统</title>
    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
    <link rel="stylesheet" href="/assets/css/tabs.css">
</head>
<body class="bg-gray-100">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" class="flex h-screen" v-cloak>
        <!-- 侧边导航栏 -->
        <tabs-sidebar
            :user="currentUser"
            :sidebar-open="sidebarOpen"
            @navigate="handleNavigate">
        </tabs-sidebar>

        <!-- 主内容区域 -->
        <div class="flex-1 flex flex-col md:ml-72">
            <!-- 标签页栏 -->
            <tabs-view
                :tabs="tabs"
                :active-tab="activeTab"
                @tab-click="handleTabClick"
                @tab-close="handleTabClose"
                @tab-close-others="handleTabCloseOthers"
                @tab-close-all="handleTabCloseAll">
            </tabs-view>

            <!-- 页面内容区域 -->
            <div class="flex-1 overflow-hidden">
                <page-container
                    :active-tab="activeTab"
                    :tabs="tabs">
                </page-container>
            </div>
        </div>

        <!-- 移动端遮罩 -->
        <div v-if="sidebarOpen" 
             @click="closeSidebar"
             class="fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden">
        </div>
    </div>

    <!-- 引入依赖 -->
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/global.js"></script>
    <script type="module" src="/scripts/config.js"></script>
    
    <!-- 引入标签页相关组件和脚本 -->
    <script type="module" src="/components/tabs/TabsSidebar.js"></script>
    <script type="module" src="/components/tabs/TabsView.js"></script>
    <script type="module" src="/components/tabs/PageContainer.js"></script>
    <script type="module" src="/scripts/stores/tabsStore.js"></script>
    <script type="module" src="/scripts/pages/tabs.js"></script>
</body>
</html>

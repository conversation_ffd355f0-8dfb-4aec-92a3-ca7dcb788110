/**
 * 标签页模式的侧边栏组件
 * 基于原有Sidebar组件，增加标签页导航功能
 */

import { logout } from '../../scripts/api/auth.js';
import { getPendingApplications } from '../../scripts/api/application.js';

export default {
    props: {
        user: Object,
        sidebarOpen: {
            type: Boolean,
            default: false
        }
    },
    emits: ['navigate'],
    setup(props, { emit }) {
        const { ref, computed, onMounted, onUnmounted } = Vue;

        // 响应式用户信息状态
        const sessionUser = ref(null);

        // 从sessionStorage加载用户信息
        function loadUserFromSession() {
            try {
                const userJson = sessionStorage.getItem('user');
                if (userJson) {
                    sessionUser.value = JSON.parse(userJson);
                }
            } catch (error) {
                console.warn('无法从sessionStorage获取用户信息:', error);
                sessionUser.value = null;
            }
        }

        // 智能用户信息获取
        const currentUser = computed(() => {
            if (props.user) {
                return props.user;
            }
            return sessionUser.value;
        });

        // 权限检查函数
        function hasPermission(permission) {
            if (!currentUser.value) return false;
            
            // admin角色拥有所有权限
            if (currentUser.value.role === 'admin') return true;
            
            const userPermissions = currentUser.value.permissions || [];
            return userPermissions.includes(permission);
        }

        // 菜单展开状态
        const applicationManagementExpanded = ref(false);
        const scheduleManagementExpanded = ref(false);
        const equipmentManagementExpanded = ref(false);
        const systemManagementExpanded = ref(false);
        const qualityManagementExpanded = ref(false);
        const fileManagementExpanded = ref(false);
        const intelligentSchedulingExpanded = ref(false);
        const productManagementExpanded = ref(false);
        const warehouseManagementExpanded = ref(false);
        const newApplicationExpanded = ref(false);

        // 待审核数量
        const pendingCount = ref(0);

        // 页面导航映射
        const pageRoutes = {
            // 主页
            'dashboard': {
                path: '/dashboard',
                title: '主页',
                icon: '🏠'
            },
            
            // 申请管理
            'new-application-standard': {
                path: '/application/new',
                title: '标准申请',
                icon: '📝'
            },
            'new-application-other': {
                path: '/application/new-other',
                title: '其他申请',
                icon: '📋'
            },
            'application-record': {
                path: '/application/record',
                title: '申请记录',
                icon: '📄'
            },
            'pending-approval': {
                path: '/application/pending',
                title: '待审核申请',
                icon: '⏳'
            },
            'approved-applications': {
                path: '/application/approved',
                title: '已审核申请',
                icon: '✅'
            },
            
            // 设备管理
            'equipment-info': {
                path: '/equipment/info',
                title: '设备信息',
                icon: '⚙️'
            },
            'equipment-maintenance': {
                path: '/equipment/maintenance',
                title: '维护记录',
                icon: '🔧'
            },
            'equipment-health': {
                path: '/equipment/health',
                title: '健康监控',
                icon: '💚'
            },
            'equipment-capacity': {
                path: '/equipment/capacity',
                title: '产能配置',
                icon: '📊'
            },
            
            // 生产排程
            'schedule-dashboard': {
                path: '/schedule/dashboard',
                title: '排程概览',
                icon: '📅'
            },
            'schedule-list': {
                path: '/schedule/list',
                title: '排程列表',
                icon: '📋'
            },
            'schedule-create': {
                path: '/schedule/create',
                title: '创建排程',
                icon: '➕'
            },
            'intelligent-scheduling': {
                path: '/scheduling/intelligent',
                title: '智能排程',
                icon: '🤖'
            },
            'delivery-prediction': {
                path: '/scheduling/delivery-prediction',
                title: '交期预测',
                icon: '🔮'
            },
            'algorithm-tuning': {
                path: '/admin/algorithm-tuning',
                title: '算法调优',
                icon: '⚡'
            },
            
            // 产品管理
            'product-management': {
                path: '/product/management',
                title: '产品管理',
                icon: '📦'
            },
            'product-processes': {
                path: '/product/processes',
                title: '工艺流程',
                icon: '🔄'
            },
            'operator-management': {
                path: '/operator/management',
                title: '操作员管理',
                icon: '👷'
            },
            
            // 仓库管理
            'warehouse-materials-products': {
                path: '/warehouse/materials-products',
                title: '物料成品管理',
                icon: '📦'
            },
            'warehouse-inventory': {
                path: '/warehouse/inventory',
                title: '库存监控',
                icon: '📊'
            },
            'warehouse-transactions': {
                path: '/warehouse/transactions',
                title: '操作记录',
                icon: '📝'
            },
            'warehouse-reports': {
                path: '/warehouse/reports',
                title: '报表追溯',
                icon: '📈'
            },
            
            // 质量管理
            'quality-reports': {
                path: '/quality/reports',
                title: '检测报告',
                icon: '🔍'
            },
            'quality-upload': {
                path: '/quality/upload',
                title: '上传报告',
                icon: '📤'
            },
            
            // 文件管理
            'file-management': {
                path: '/file-management',
                title: '文件管理',
                icon: '📁'
            },
            
            // 系统管理
            'user-management': {
                path: '/user/management',
                title: '用户管理',
                icon: '👥'
            },
            'customer-management': {
                path: '/customer/management',
                title: '客户管理',
                icon: '🏢'
            },
            'system-logs': {
                path: '/system/logs',
                title: '系统日志',
                icon: '📋'
            }
        };

        // 处理导航点击
        function handleNavigate(routeKey, event) {
            if (event) {
                event.preventDefault();
            }
            
            const route = pageRoutes[routeKey];
            if (route) {
                emit('navigate', {
                    path: route.path,
                    title: route.title,
                    icon: route.icon
                });
            }
        }

        // 菜单切换函数
        function toggleApplicationManagementMenu() {
            applicationManagementExpanded.value = !applicationManagementExpanded.value;
        }

        function toggleScheduleManagementMenu() {
            scheduleManagementExpanded.value = !scheduleManagementExpanded.value;
        }

        function toggleEquipmentManagementMenu() {
            equipmentManagementExpanded.value = !equipmentManagementExpanded.value;
        }

        function toggleSystemManagementMenu() {
            systemManagementExpanded.value = !systemManagementExpanded.value;
        }

        function toggleQualityManagementMenu() {
            qualityManagementExpanded.value = !qualityManagementExpanded.value;
        }

        function toggleFileManagementMenu() {
            fileManagementExpanded.value = !fileManagementExpanded.value;
        }

        function toggleProductManagementMenu() {
            productManagementExpanded.value = !productManagementExpanded.value;
        }

        function toggleWarehouseManagementMenu() {
            warehouseManagementExpanded.value = !warehouseManagementExpanded.value;
        }

        function toggleIntelligentSchedulingMenu() {
            intelligentSchedulingExpanded.value = !intelligentSchedulingExpanded.value;
        }

        function toggleNewApplicationMenu() {
            newApplicationExpanded.value = !newApplicationExpanded.value;
        }

        // 加载待审核数量
        async function loadPendingCount() {
            if (!currentUser.value) return;
            
            try {
                const response = await getPendingApplications();
                if (response.success && response.data) {
                    pendingCount.value = response.data.length;
                }
            } catch (error) {
                console.warn('获取待审核数量失败:', error);
            }
        }

        // 退出登录
        function handleLogout() {
            logout();
        }

        // 监听用户信息更新事件
        function handleUserUpdated(event) {
            sessionUser.value = event.detail;
        }

        // 生命周期
        onMounted(() => {
            loadUserFromSession();
            window.addEventListener('userUpdated', handleUserUpdated);
            loadPendingCount();
        });

        onUnmounted(() => {
            window.removeEventListener('userUpdated', handleUserUpdated);
        });

        return {
            currentUser,
            hasPermission,
            handleNavigate,
            handleLogout,
            pendingCount,
            
            // 菜单展开状态
            applicationManagementExpanded,
            scheduleManagementExpanded,
            equipmentManagementExpanded,
            systemManagementExpanded,
            qualityManagementExpanded,
            fileManagementExpanded,
            intelligentSchedulingExpanded,
            productManagementExpanded,
            warehouseManagementExpanded,
            newApplicationExpanded,
            
            // 菜单切换函数
            toggleApplicationManagementMenu,
            toggleScheduleManagementMenu,
            toggleEquipmentManagementMenu,
            toggleSystemManagementMenu,
            toggleQualityManagementMenu,
            toggleFileManagementMenu,
            toggleProductManagementMenu,
            toggleWarehouseManagementMenu,
            toggleIntelligentSchedulingMenu,
            toggleNewApplicationMenu
        };
    },
    template: `
        <div class="sidebar bg-white text-gray-700 w-64 h-screen fixed left-0 top-0 flex flex-col border-r border-gray-100 shadow-md z-10 transform -translate-x-full md:translate-x-0 transition-transform duration-300 ease-in-out" :class="{'translate-x-0': sidebarOpen}">
            <!-- 顶部Logo区域 -->
            <div class="flex-shrink-0 p-4 border-b border-gray-100">
                <h1 class="text-lg font-medium text-gray-800 flex items-center">
                    <img src="/logo/Makrite-logo.png" alt="Makrite Logo" class="w-16 h-16 mr-3 object-contain">
                    管理系统
                </h1>
                <div class="text-xs text-gray-500 mt-1">标签页模式</div>
            </div>

            <!-- 导航菜单区域 -->
            <nav class="flex-1 overflow-y-auto px-2 py-3 min-h-0">
                <ul class="space-y-1">
                    <!-- 主页 -->
                    <li>
                        <button @click="handleNavigate('dashboard')"
                               class="sidebar-btn w-full text-left flex items-center px-3 py-2.5 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900">
                            <span class="mr-3">🏠</span>
                            主页
                        </button>
                    </li>
                    
                    <!-- 申请管理菜单 -->
                    <li v-if="hasPermission('new_application') || hasPermission('application_record') || hasPermission('pending_approval') || hasPermission('approved_applications')">
                        <button @click="toggleApplicationManagementMenu"
                               class="sidebar-btn w-full text-left flex items-center justify-between px-3 py-2.5 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900">
                            <div class="flex items-center">
                                <span class="mr-3">📝</span>
                                申请管理
                            </div>
                            <svg :class="['w-4 h-4 transition-transform', applicationManagementExpanded ? 'rotate-90' : '']" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </button>
                        
                        <!-- 申请管理子菜单 -->
                        <ul v-show="applicationManagementExpanded" class="ml-6 mt-1 space-y-1">
                            <li v-if="hasPermission('new_application')">
                                <button @click="handleNavigate('new-application-standard')"
                                       class="sidebar-btn w-full text-left flex items-center px-3 py-2 rounded-md text-sm text-gray-600 hover:bg-gray-50 hover:text-gray-900">
                                    <span class="mr-2">📝</span>
                                    标准申请
                                </button>
                            </li>
                            <li v-if="hasPermission('application_record')">
                                <button @click="handleNavigate('application-record')"
                                       class="sidebar-btn w-full text-left flex items-center px-3 py-2 rounded-md text-sm text-gray-600 hover:bg-gray-50 hover:text-gray-900">
                                    <span class="mr-2">📄</span>
                                    申请记录
                                </button>
                            </li>
                            <li v-if="hasPermission('pending_approval')">
                                <button @click="handleNavigate('pending-approval')"
                                       class="sidebar-btn w-full text-left flex items-center px-3 py-2 rounded-md text-sm text-gray-600 hover:bg-gray-50 hover:text-gray-900">
                                    <span class="mr-2">⏳</span>
                                    待审核申请
                                    <span v-if="pendingCount > 0" class="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-0.5">{{ pendingCount }}</span>
                                </button>
                            </li>
                        </ul>
                    </li>
                    
                    <!-- 其他菜单项将在后续添加 -->
                </ul>
            </nav>

            <!-- 底部用户信息 -->
            <div class="flex-shrink-0 p-4 border-t border-gray-100">
                <div v-if="currentUser" class="flex items-center">
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">{{ currentUser.username }}</p>
                        <p class="text-xs text-gray-500 truncate">{{ currentUser.department || '未设置部门' }}</p>
                    </div>
                    <button @click="handleLogout" 
                           class="ml-3 p-2 text-gray-400 hover:text-gray-600 transition-colors"
                           title="退出登录">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    `
};

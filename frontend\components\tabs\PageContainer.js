/**
 * 页面容器组件
 * 管理标签页内容的显示和缓存
 */

export default {
    props: {
        activeTab: {
            type: Object,
            default: null
        },
        tabs: {
            type: Array,
            default: () => []
        }
    },
    setup(props) {
        const { ref, computed, watch, onMounted, onUnmounted } = Vue;

        // 页面组件缓存
        const componentCache = ref(new Map());
        const loadingStates = ref(new Map());
        const errorStates = ref(new Map());

        // 当前显示的组件
        const currentComponent = ref(null);
        const isLoading = ref(false);
        const error = ref(null);

        // 页面路由映射
        const pageRoutes = {
            '/dashboard': () => import('/scripts/pages/dashboard.js'),
            '/user/management': () => import('/scripts/pages/system-management/user-management.js'),
            '/application/record': () => import('/scripts/pages/application/record.js'),
            '/application/new': () => import('/scripts/pages/application/new.js'),
            '/application/pending': () => import('/scripts/pages/application/pending.js'),
            '/application/approved': () => import('/scripts/pages/application/approved.js'),
            '/schedule/dashboard': () => import('/scripts/pages/schedule/dashboard.js'),
            '/schedule/list': () => import('/scripts/pages/schedule/list.js'),
            '/equipment/info': () => import('/scripts/pages/equipment/info.js'),
            '/equipment/health': () => import('/scripts/pages/equipment/health.js'),
            '/warehouse/materials-products': () => import('/scripts/pages/warehouse/materials-products.js'),
            '/quality/reports': () => import('/scripts/pages/quality/reports.js')
        };

        // 加载页面组件
        async function loadPageComponent(path) {
            if (!path) return null;

            // 检查缓存
            if (componentCache.value.has(path)) {
                return componentCache.value.get(path);
            }

            // 设置加载状态
            loadingStates.value.set(path, true);
            errorStates.value.delete(path);

            try {
                // 动态导入组件
                const moduleLoader = pageRoutes[path];
                if (!moduleLoader) {
                    throw new Error(`未找到页面路由: ${path}`);
                }

                const module = await moduleLoader();
                const component = module.default || module;

                // 缓存组件
                componentCache.value.set(path, component);
                loadingStates.value.set(path, false);

                console.log('页面组件加载成功:', path);
                return component;

            } catch (err) {
                console.error('页面组件加载失败:', path, err);
                errorStates.value.set(path, err.message || '加载失败');
                loadingStates.value.set(path, false);
                return null;
            }
        }

        // 创建页面组件包装器
        function createPageWrapper(component, tab) {
            if (!component) return null;

            return {
                name: `PageWrapper_${tab.id}`,
                setup() {
                    // 页面级别的状态管理
                    const pageState = ref({
                        scrollTop: 0,
                        formData: {},
                        filters: {},
                        selectedItems: []
                    });

                    // 保存页面状态
                    function savePageState() {
                        const container = document.querySelector('.page-content');
                        if (container) {
                            pageState.value.scrollTop = container.scrollTop;
                        }
                    }

                    // 恢复页面状态
                    function restorePageState() {
                        Vue.nextTick(() => {
                            const container = document.querySelector('.page-content');
                            if (container && pageState.value.scrollTop > 0) {
                                container.scrollTop = pageState.value.scrollTop;
                            }
                        });
                    }

                    // 页面激活时恢复状态
                    onMounted(() => {
                        restorePageState();
                    });

                    // 页面失活时保存状态
                    onUnmounted(() => {
                        savePageState();
                    });

                    return {
                        pageState,
                        savePageState,
                        restorePageState
                    };
                },
                render() {
                    return Vue.h(component, {
                        key: tab.id,
                        tabId: tab.id,
                        tabPath: tab.path
                    });
                }
            };
        }

        // 监听活跃标签页变化
        watch(
            () => props.activeTab,
            async (newTab, oldTab) => {
                if (!newTab) {
                    currentComponent.value = null;
                    isLoading.value = false;
                    error.value = null;
                    return;
                }

                // 如果是同一个标签页，不需要重新加载
                if (oldTab && newTab.id === oldTab.id) {
                    return;
                }

                // 设置加载状态
                isLoading.value = true;
                error.value = null;

                try {
                    // 加载页面组件
                    const component = await loadPageComponent(newTab.path);
                    
                    if (component) {
                        // 创建页面包装器
                        currentComponent.value = createPageWrapper(component, newTab);
                    } else {
                        error.value = '页面组件加载失败';
                    }
                } catch (err) {
                    console.error('切换标签页失败:', err);
                    error.value = err.message || '页面加载失败';
                } finally {
                    isLoading.value = false;
                }
            },
            { immediate: true }
        );

        // 清理缓存
        function clearCache() {
            componentCache.value.clear();
            loadingStates.value.clear();
            errorStates.value.clear();
        }

        // 移除特定页面的缓存
        function removeCacheForPath(path) {
            componentCache.value.delete(path);
            loadingStates.value.delete(path);
            errorStates.value.delete(path);
        }

        // 获取缓存统计
        const cacheStats = computed(() => ({
            total: componentCache.value.size,
            loading: Array.from(loadingStates.value.values()).filter(Boolean).length,
            errors: errorStates.value.size
        }));

        return {
            currentComponent,
            isLoading,
            error,
            cacheStats,
            clearCache,
            removeCacheForPath
        };
    },
    template: `
        <div class="page-container">
            <!-- 加载状态 -->
            <div v-if="isLoading" class="flex items-center justify-center h-full">
                <div class="text-center">
                    <div class="loading-spinner mx-auto mb-4"></div>
                    <p class="text-gray-600">正在加载页面...</p>
                </div>
            </div>
            
            <!-- 错误状态 -->
            <div v-else-if="error" class="flex items-center justify-center h-full">
                <div class="text-center">
                    <div class="text-red-500 text-6xl mb-4">⚠️</div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">页面加载失败</h3>
                    <p class="text-gray-600 mb-4">{{ error }}</p>
                    <button 
                        @click="$emit('reload')"
                        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                        重新加载
                    </button>
                </div>
            </div>
            
            <!-- 页面内容 -->
            <div v-else-if="currentComponent" class="page-content h-full overflow-auto">
                <component :is="currentComponent" />
            </div>
            
            <!-- 空状态 -->
            <div v-else class="flex items-center justify-center h-full">
                <div class="text-center">
                    <div class="text-gray-400 text-6xl mb-4">📄</div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">暂无内容</h3>
                    <p class="text-gray-600">请从侧边栏选择要打开的页面</p>
                </div>
            </div>
        </div>
    `
};

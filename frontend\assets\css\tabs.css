/**
 * 标签页模式样式
 * 用于标签页界面的样式定义
 */

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 隐藏元素直到Vue加载完成 */
[v-cloak] {
    display: none !important;
}

/* 标签页栏样式 */
.tabs-bar {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    min-height: 48px;
    display: flex;
    align-items: center;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.tabs-bar::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

/* 标签页项样式 */
.tab-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    border-right: 1px solid #e5e7eb;
    background: #f9fafb;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    min-width: 120px;
    max-width: 200px;
    position: relative;
}

.tab-item:hover {
    background: #f3f4f6;
    color: #374151;
}

.tab-item.active {
    background: white;
    color: #3b82f6;
    border-bottom: 2px solid #3b82f6;
}

.tab-item.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: #3b82f6;
}

/* 标签页标题 */
.tab-title {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    font-weight: 500;
}

/* 标签页关闭按钮 */
.tab-close {
    margin-left: 8px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9ca3af;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.tab-close:hover {
    background: #e5e7eb;
    color: #374151;
}

.tab-item.active .tab-close:hover {
    background: #dbeafe;
    color: #3b82f6;
}

/* 页面容器样式 */
.page-container {
    height: 100%;
    overflow: hidden;
}

.page-content {
    height: 100%;
    overflow: auto;
}

/* 标签页右键菜单 */
.tab-context-menu {
    position: fixed;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    min-width: 120px;
    padding: 4px 0;
}

.tab-context-menu-item {
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    color: #374151;
    transition: background-color 0.2s ease;
}

.tab-context-menu-item:hover {
    background: #f3f4f6;
}

.tab-context-menu-item.disabled {
    color: #9ca3af;
    cursor: not-allowed;
}

.tab-context-menu-item.disabled:hover {
    background: transparent;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .tab-item {
        min-width: 100px;
        max-width: 150px;
        padding: 6px 12px;
    }
    
    .tab-title {
        font-size: 13px;
    }
    
    .tab-close {
        width: 14px;
        height: 14px;
        margin-left: 6px;
    }
}

/* 标签页拖拽样式 */
.tab-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.tab-item.drag-over {
    border-left: 2px solid #3b82f6;
}

/* 新标签页按钮 */
.new-tab-btn {
    padding: 8px 12px;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
    border-right: 1px solid #e5e7eb;
}

.new-tab-btn:hover {
    background: #f3f4f6;
    color: #374151;
}

/* 标签页操作按钮组 */
.tabs-actions {
    display: flex;
    align-items: center;
    padding: 0 8px;
    border-left: 1px solid #e5e7eb;
    background: white;
}

.tabs-action-btn {
    padding: 6px;
    color: #6b7280;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s ease;
    margin: 0 2px;
}

.tabs-action-btn:hover {
    background: #f3f4f6;
    color: #374151;
}

/**
 * 标签页视图组件
 * 显示和管理标签页列表
 */

export default {
    props: {
        tabs: {
            type: Array,
            default: () => []
        },
        activeTab: {
            type: Object,
            default: null
        }
    },
    emits: ['tab-click', 'tab-close', 'tab-close-others', 'tab-close-all'],
    setup(props, { emit }) {
        const { ref, computed, onMounted, onUnmounted } = Vue;

        // 右键菜单状态
        const contextMenu = ref({
            visible: false,
            x: 0,
            y: 0,
            targetTabId: null
        });

        // 拖拽状态
        const dragState = ref({
            dragging: false,
            dragTabId: null,
            dragOverTabId: null
        });

        // 计算属性
        const hasMultipleTabs = computed(() => props.tabs.length > 1);
        const hasClosableTabs = computed(() => 
            props.tabs.some(tab => tab.closable && !tab.pinned)
        );

        // 标签页点击
        function handleTabClick(tab) {
            if (tab.id !== props.activeTab?.id) {
                emit('tab-click', tab.id);
            }
        }

        // 标签页关闭
        function handleTabClose(tab, event) {
            event.stopPropagation();
            if (tab.closable && !tab.pinned) {
                emit('tab-close', tab.id);
            }
        }

        // 标签页右键菜单
        function handleTabContextMenu(tab, event) {
            event.preventDefault();
            contextMenu.value = {
                visible: true,
                x: event.clientX,
                y: event.clientY,
                targetTabId: tab.id
            };
        }

        // 关闭右键菜单
        function closeContextMenu() {
            contextMenu.value.visible = false;
        }

        // 右键菜单操作
        function handleContextMenuAction(action) {
            const tabId = contextMenu.value.targetTabId;
            
            switch (action) {
                case 'close':
                    emit('tab-close', tabId);
                    break;
                case 'close-others':
                    emit('tab-close-others', tabId);
                    break;
                case 'close-all':
                    emit('tab-close-all');
                    break;
                case 'pin':
                    // TODO: 实现固定标签页功能
                    break;
            }
            
            closeContextMenu();
        }

        // 标签页拖拽开始
        function handleDragStart(tab, event) {
            dragState.value.dragging = true;
            dragState.value.dragTabId = tab.id;
            event.dataTransfer.effectAllowed = 'move';
            event.dataTransfer.setData('text/plain', tab.id);
        }

        // 标签页拖拽结束
        function handleDragEnd() {
            dragState.value.dragging = false;
            dragState.value.dragTabId = null;
            dragState.value.dragOverTabId = null;
        }

        // 标签页拖拽悬停
        function handleDragOver(tab, event) {
            event.preventDefault();
            if (dragState.value.dragTabId && dragState.value.dragTabId !== tab.id) {
                dragState.value.dragOverTabId = tab.id;
            }
        }

        // 标签页拖拽离开
        function handleDragLeave() {
            dragState.value.dragOverTabId = null;
        }

        // 标签页放置
        function handleDrop(tab, event) {
            event.preventDefault();
            const dragTabId = event.dataTransfer.getData('text/plain');
            
            if (dragTabId && dragTabId !== tab.id) {
                // TODO: 实现标签页重排序
                console.log('重排序标签页:', dragTabId, '->', tab.id);
            }
            
            handleDragEnd();
        }

        // 获取标签页图标
        function getTabIcon(tab) {
            if (tab.icon) return tab.icon;
            
            // 根据路径返回默认图标
            if (tab.path.includes('dashboard')) return '🏠';
            if (tab.path.includes('application')) return '📝';
            if (tab.path.includes('schedule')) return '📅';
            if (tab.path.includes('equipment')) return '⚙️';
            if (tab.path.includes('warehouse')) return '📦';
            if (tab.path.includes('quality')) return '🔍';
            if (tab.path.includes('user')) return '👥';
            return '📄';
        }

        // 点击外部关闭右键菜单
        function handleClickOutside(event) {
            if (contextMenu.value.visible) {
                closeContextMenu();
            }
        }

        // 生命周期
        onMounted(() => {
            document.addEventListener('click', handleClickOutside);
        });

        onUnmounted(() => {
            document.removeEventListener('click', handleClickOutside);
        });

        return {
            contextMenu,
            dragState,
            hasMultipleTabs,
            hasClosableTabs,
            handleTabClick,
            handleTabClose,
            handleTabContextMenu,
            handleContextMenuAction,
            handleDragStart,
            handleDragEnd,
            handleDragOver,
            handleDragLeave,
            handleDrop,
            getTabIcon
        };
    },
    template: `
        <div class="tabs-bar">
            <!-- 标签页列表 -->
            <div class="flex flex-1 overflow-x-auto">
                <div
                    v-for="tab in tabs"
                    :key="tab.id"
                    :class="[
                        'tab-item',
                        { 
                            'active': activeTab && tab.id === activeTab.id,
                            'dragging': dragState.dragTabId === tab.id,
                            'drag-over': dragState.dragOverTabId === tab.id
                        }
                    ]"
                    :draggable="true"
                    @click="handleTabClick(tab)"
                    @contextmenu="handleTabContextMenu(tab, $event)"
                    @dragstart="handleDragStart(tab, $event)"
                    @dragend="handleDragEnd"
                    @dragover="handleDragOver(tab, $event)"
                    @dragleave="handleDragLeave"
                    @drop="handleDrop(tab, $event)">
                    
                    <!-- 标签页图标 -->
                    <span class="mr-2 text-sm">{{ getTabIcon(tab) }}</span>
                    
                    <!-- 标签页标题 -->
                    <span class="tab-title" :title="tab.title">{{ tab.title }}</span>
                    
                    <!-- 固定标识 -->
                    <span v-if="tab.pinned" class="ml-1 text-xs">📌</span>
                    
                    <!-- 关闭按钮 -->
                    <button
                        v-if="tab.closable && !tab.pinned"
                        class="tab-close"
                        @click="handleTabClose(tab, $event)"
                        :title="'关闭 ' + tab.title">
                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
            
            <!-- 标签页操作按钮 -->
            <div class="tabs-actions">
                <!-- 关闭其他标签页 -->
                <button
                    v-if="hasMultipleTabs"
                    class="tabs-action-btn"
                    @click="$emit('tab-close-others', activeTab?.id)"
                    title="关闭其他标签页">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
                
                <!-- 关闭所有标签页 -->
                <button
                    v-if="hasClosableTabs"
                    class="tabs-action-btn"
                    @click="$emit('tab-close-all')"
                    title="关闭所有标签页">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </button>
            </div>
            
            <!-- 右键菜单 -->
            <div
                v-if="contextMenu.visible"
                class="tab-context-menu"
                :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }">
                
                <div class="tab-context-menu-item" @click="handleContextMenuAction('close')">
                    关闭标签页
                </div>
                
                <div 
                    v-if="hasMultipleTabs"
                    class="tab-context-menu-item" 
                    @click="handleContextMenuAction('close-others')">
                    关闭其他标签页
                </div>
                
                <div 
                    v-if="hasClosableTabs"
                    class="tab-context-menu-item" 
                    @click="handleContextMenuAction('close-all')">
                    关闭所有标签页
                </div>
                
                <div class="border-t border-gray-200 my-1"></div>
                
                <div class="tab-context-menu-item" @click="handleContextMenuAction('pin')">
                    固定标签页
                </div>
            </div>
        </div>
    `
};

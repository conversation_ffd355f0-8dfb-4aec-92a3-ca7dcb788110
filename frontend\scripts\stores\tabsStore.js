/**
 * 标签页状态管理
 * 管理标签页的状态、持久化和操作
 */

const { reactive, computed, watch } = Vue;

// 标签页存储键
const TABS_STORAGE_KEY = 'makrite_tabs_state';
const MAX_TABS = 20; // 最大标签页数量

// 创建标签页状态管理
export function createTabsStore() {
    // 响应式状态
    const state = reactive({
        tabs: [], // 标签页列表
        activeTabId: null, // 当前活跃的标签页ID
        history: [], // 访问历史
        settings: {
            maxTabs: MAX_TABS,
            enablePersistence: true,
            enableCache: true
        }
    });

    // 计算属性
    const activeTab = computed(() => {
        return state.tabs.find(tab => tab.id === state.activeTabId) || null;
    });

    const tabsCount = computed(() => state.tabs.length);

    const canCloseTab = computed(() => (tabId) => {
        const tab = state.tabs.find(t => t.id === tabId);
        return tab && !tab.pinned && state.tabs.length > 1;
    });

    // 生成标签页ID
    function generateTabId() {
        return `tab_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // 创建标签页对象
    function createTab(options) {
        const {
            id = generateTabId(),
            title,
            path,
            component,
            icon = null,
            closable = true,
            pinned = false,
            cached = true,
            meta = {}
        } = options;

        return {
            id,
            title,
            path,
            component,
            icon,
            closable,
            pinned,
            cached,
            meta,
            createdAt: new Date().toISOString(),
            lastActiveAt: new Date().toISOString()
        };
    }

    // 添加标签页
    function addTab(options) {
        // 检查是否已存在相同路径的标签页
        const existingTab = state.tabs.find(tab => tab.path === options.path);
        if (existingTab) {
            // 如果已存在，激活该标签页
            setActiveTab(existingTab.id);
            return existingTab;
        }

        // 检查标签页数量限制
        if (state.tabs.length >= state.settings.maxTabs) {
            // 移除最旧的非固定标签页
            const oldestTab = state.tabs
                .filter(tab => !tab.pinned && tab.closable)
                .sort((a, b) => new Date(a.lastActiveAt) - new Date(b.lastActiveAt))[0];
            
            if (oldestTab) {
                removeTab(oldestTab.id);
            } else {
                console.warn('无法添加更多标签页：已达到最大数量限制');
                return null;
            }
        }

        // 创建新标签页
        const newTab = createTab(options);
        state.tabs.push(newTab);
        
        // 激活新标签页
        setActiveTab(newTab.id);
        
        // 添加到历史记录
        addToHistory(newTab.path);
        
        console.log('添加标签页:', newTab.title, newTab.path);
        return newTab;
    }

    // 移除标签页
    function removeTab(tabId) {
        const tabIndex = state.tabs.findIndex(tab => tab.id === tabId);
        if (tabIndex === -1) return false;

        const tab = state.tabs[tabIndex];
        
        // 检查是否可以关闭
        if (!canCloseTab.value(tabId)) {
            console.warn('无法关闭标签页:', tab.title);
            return false;
        }

        // 如果关闭的是当前活跃标签页，需要切换到其他标签页
        if (state.activeTabId === tabId) {
            // 优先切换到右侧标签页，如果没有则切换到左侧
            const nextTab = state.tabs[tabIndex + 1] || state.tabs[tabIndex - 1];
            if (nextTab) {
                setActiveTab(nextTab.id);
            } else {
                state.activeTabId = null;
            }
        }

        // 移除标签页
        state.tabs.splice(tabIndex, 1);
        
        console.log('移除标签页:', tab.title);
        return true;
    }

    // 设置活跃标签页
    function setActiveTab(tabId) {
        const tab = state.tabs.find(t => t.id === tabId);
        if (!tab) {
            console.warn('标签页不存在:', tabId);
            return false;
        }

        state.activeTabId = tabId;
        tab.lastActiveAt = new Date().toISOString();
        
        // 更新浏览器URL（不刷新页面）
        if (window.history && tab.path) {
            const newUrl = `/tabs#${tab.path}`;
            if (window.location.href !== window.location.origin + newUrl) {
                window.history.replaceState({ tabId, path: tab.path }, tab.title, newUrl);
            }
        }
        
        console.log('切换到标签页:', tab.title);
        return true;
    }

    // 关闭其他标签页
    function closeOtherTabs(keepTabId) {
        const tabsToClose = state.tabs.filter(tab => 
            tab.id !== keepTabId && !tab.pinned && tab.closable
        );
        
        tabsToClose.forEach(tab => removeTab(tab.id));
        
        // 确保保留的标签页是活跃的
        if (keepTabId && state.activeTabId !== keepTabId) {
            setActiveTab(keepTabId);
        }
    }

    // 关闭所有标签页
    function closeAllTabs() {
        const closableTabs = state.tabs.filter(tab => !tab.pinned && tab.closable);
        closableTabs.forEach(tab => removeTab(tab.id));
        
        // 如果没有标签页了，跳转到主页
        if (state.tabs.length === 0) {
            window.location.href = '/dashboard';
        }
    }

    // 固定/取消固定标签页
    function toggleTabPin(tabId) {
        const tab = state.tabs.find(t => t.id === tabId);
        if (tab) {
            tab.pinned = !tab.pinned;
            console.log(tab.pinned ? '固定标签页:' : '取消固定标签页:', tab.title);
        }
    }

    // 添加到历史记录
    function addToHistory(path) {
        const index = state.history.indexOf(path);
        if (index > -1) {
            state.history.splice(index, 1);
        }
        state.history.unshift(path);
        
        // 限制历史记录数量
        if (state.history.length > 50) {
            state.history = state.history.slice(0, 50);
        }
    }

    // 保存状态到localStorage
    function saveState() {
        if (!state.settings.enablePersistence) return;
        
        try {
            const stateToSave = {
                tabs: state.tabs.map(tab => ({
                    ...tab,
                    // 不保存组件实例，只保存基本信息
                    component: null
                })),
                activeTabId: state.activeTabId,
                history: state.history.slice(0, 10), // 只保存最近10条历史
                settings: state.settings
            };
            
            localStorage.setItem(TABS_STORAGE_KEY, JSON.stringify(stateToSave));
        } catch (error) {
            console.warn('保存标签页状态失败:', error);
        }
    }

    // 从localStorage加载状态
    function loadState() {
        if (!state.settings.enablePersistence) return;
        
        try {
            const savedState = localStorage.getItem(TABS_STORAGE_KEY);
            if (savedState) {
                const parsed = JSON.parse(savedState);
                
                // 恢复设置
                if (parsed.settings) {
                    Object.assign(state.settings, parsed.settings);
                }
                
                // 恢复历史记录
                if (parsed.history) {
                    state.history = parsed.history;
                }
                
                // 恢复标签页（但不恢复组件，需要重新加载）
                if (parsed.tabs && parsed.tabs.length > 0) {
                    state.tabs = parsed.tabs;
                    state.activeTabId = parsed.activeTabId;
                    
                    console.log('恢复标签页状态:', state.tabs.length, '个标签页');
                }
            }
        } catch (error) {
            console.warn('加载标签页状态失败:', error);
            // 清除损坏的状态
            localStorage.removeItem(TABS_STORAGE_KEY);
        }
    }

    // 清除状态
    function clearState() {
        state.tabs = [];
        state.activeTabId = null;
        state.history = [];
        localStorage.removeItem(TABS_STORAGE_KEY);
    }

    // 监听状态变化，自动保存
    watch(
        () => ({ tabs: state.tabs, activeTabId: state.activeTabId }),
        () => saveState(),
        { deep: true }
    );

    // 返回公共API
    return {
        // 状态
        state,
        activeTab,
        tabsCount,
        canCloseTab,
        
        // 方法
        addTab,
        removeTab,
        setActiveTab,
        closeOtherTabs,
        closeAllTabs,
        toggleTabPin,
        saveState,
        loadState,
        clearState
    };
}

// 创建全局标签页store实例
export const tabsStore = createTabsStore();
